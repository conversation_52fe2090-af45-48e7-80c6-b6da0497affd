torch==2.4.0
absl-py==2.1.0
accelerate==1.2.1
aiohappyeyeballs==2.4.4
aiohttp==3.11.11
aiosignal==1.3.2
annotated-types==0.7.0
async-timeout==5.0.1
attrs==24.2.0
certifi==2024.7.4
charset-normalizer==3.3.2
click==8.1.7
datasets==3.2.0
deepspeed==0.14.5
dill==0.3.8
docstring_parser==0.16
einops==0.8.0
et-xmlfile==1.1.0
eval_type_backport==0.2.2
filelock==3.15.4
fire==0.6.0
frozenlist==1.5.0
fsspec==2024.6.1
h5py==3.11.0
hjson==3.1.0
huggingface-hub==0.27.1
idna==3.7
importlib_metadata==8.5.0
Jinja2==3.1.4
joblib==1.4.2
jsonlines==4.0.0
markdown-it-py==3.0.0
MarkupSafe==2.1.5
mdurl==0.1.2
mpmath==1.3.0
multidict==6.1.0
multiprocess==0.70.16
networkx==3.2.1
ninja==********
nltk==3.9
numpy==1.26.4
nvidia-cublas-cu12==********
nvidia-cuda-cupti-cu12==12.1.105
nvidia-cuda-nvrtc-cu12==12.1.105
nvidia-cuda-runtime-cu12==12.1.105
nvidia-cudnn-cu12==********
nvidia-cufft-cu12==*********
nvidia-curand-cu12==**********
nvidia-cusolver-cu12==**********
nvidia-cusparse-cu12==**********
nvidia-ml-py==12.560.30
nvidia-nccl-cu12==2.20.5
nvidia-nvjitlink-cu12==12.6.20
nvidia-nvtx-cu12==12.1.105
openai==0.9.0
openpyxl==3.1.5
packaging==24.1
pandas==2.2.2
pandas-stubs==2.2.2.240807
peft==0.14.0
pillow==10.4.0
propcache==0.2.1
protobuf==5.27.3
psutil==6.0.0
py-cpuinfo==9.0.0
pyarrow==18.1.0
pydantic==2.8.2
pydantic_core==2.20.1
Pygments==2.18.0
python-dateutil==2.9.0.post0
pytz==2024.1
PyYAML==6.0.2
regex==2024.7.24
requests==2.32.3
rich==13.9.4
rouge_score==0.1.2
safetensors==0.4.4
sentencepiece==0.2.0
shtab==1.7.1
six==1.16.0
sympy==1.13.2
tensorboardX==*******
termcolor==2.4.0
tokenizers==0.21.0
tqdm==4.66.5
transformers==4.47.1
triton==3.0.0
trl==0.9.6
typeguard==4.4.1
types-pytz==2024.1.0.20240417
typing_extensions==4.12.2
tyro==0.9.5
tzdata==2024.1
urllib3==2.2.2
xxhash==3.5.0
yarl==1.18.3
zipp==3.21.0
flash_attn==2.7.3